import os

# welche endungen wir zählen wollen
extensions = ('.js', '.ts', '.tsx', '.jsx')

# ordner, die wir ignorieren wollen
exclude_dirs = {'node_modules', '.git', 'dist', 'build', '__pycache__'}

total_lines = 0
file_count = 0

for root, dirs, files in os.walk('.'):
    # ordner rausfiltern, die wir nicht wollen
    dirs[:] = [d for d in dirs if d not in exclude_dirs]
    
    for file in files:
        if file.endswith(extensions):
            filepath = os.path.join(root, file)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    line_count = sum(1 for line in lines if line.strip())
                    total_lines += line_count
                    file_count += 1
            except Exception as e:
                print(f'⚠️ konnte {filepath} nicht lesen: {e}')

print(f'📁 analysiert: {file_count} dateien')
print(f'📊 codezeilen (ohne leerzeilen): {total_lines}')
