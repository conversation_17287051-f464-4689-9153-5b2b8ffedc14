---
description: 
globs: 
alwaysApply: true
---

- Bitte jeden Text, der im Frontend angezeigt wird, auf englisch verfassen
- Bitte den Code auf englisch schreiben
- Immer mit Sicherheit in Bedachten, dass der Code von anderen Personen betrachtet werden kann
- Kommentare und Code sollen immer übersichtlich und verständlich sein, allerdings ebenfalls auf englisch
- Wenn du deutsche Kommentare oder Todo findest, diese bitte durch englischen Text ersetzen
- wenn du fertig mit einem task bist, dann schreibe einfach eine kurze, übersichtliche zusammenfassung der erledigten tasks, ohne dass du mir den code zeigen musst, aber dass ich einen schnellen überblick über deine arbeit haben kann.
bitte starte keine konsolen befehle wie npm run dev, sondern skip diese, da wir ja immer davon ausgehen, dass der generierte code 100% perfekt ist und wir möglichst wenig testen wollen. wir testen on the fly sozusagen


bitte, ganz wichtig, halluziniere nicht. halte dich strickt an die anleitung, und wenn du nicht weißt, was du tun sollst, dann frag mich