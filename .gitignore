# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/public/uploads*
# next.js
/.next/
/out/

# production
/build
done.txt
package-lock.json*
yarn.lock
yarn-error.log*
# misc
.DS_Store
*.pem
*.vscode
_todo.txt
projekt


# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.local
.env.production
# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE und Editor-Dateien
.idea/
.vscode/
*.sublime-*
*.swp
*.swo   

# Logs  
logs/
*.log

# System-Dateien
Thumbs.db
desktop.ini

# Temporäre Dateien und Cache
.cache/
.temp/
tmp/
dist/

# Sicherheitsrelevante Dateien
*.key
*.crt
*.pfx
package-lock.json
package-lock.json   
yarn.lock
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.local
.env.production
# Upload directories
public/uploads/*
!public/uploads/.gitkeep
!public/uploads/temp/
!public/uploads/original/
!public/uploads/thumbnails/
/public/uploads/temp/*
!public/uploads/temp/.gitkeep
/public/uploads/original/*
!public/uploads/original/.gitkeep
/public/uploads/thumbnails/*
!public/uploads/thumbnails/.gitkeep
!public/uploads/temp/placeholder.jpg

# misc
.DS_Store
*.pem
*.vscode
_todo.txt
projekt


# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env.local
.env.production
# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE und Editor-Dateien
.idea/
.vscode/
*.sublime-*
*.swp
*.swo

# Logs
logs/
*.log

# System-Dateien
Thumbs.db
desktop.ini

# Temporäre Dateien und Cache
.cache/
.temp/
tmp/
dist/

# Sicherheitsrelevante Dateien
*.key
*.crt
*.pfx
package-lock.json
package-lock.json
package-lock.json
.trae/.ignore
package-lock.json
